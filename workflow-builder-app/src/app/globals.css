@import "tailwindcss";
@import "tw-animate-css";
@import "reactflow/dist/style.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-brand-primary: var(--brand-primary);
  --color-brand-secondary: var(--brand-secondary);
  --color-brand-tertiary: var(--brand-tertiary);
  --color-brand-white-text: var(--brand-white-text);
  --color-brand-primary-font: var(--brand-primary-font);
  --color-brand-secondary-font: var(--brand-secondary-font);
  --color-brand-overlay: var(--brand-overlay);
  --color-brand-card-hover: var(--brand-card-hover);
  --color-brand-background: var(--brand-background);
  --color-brand-card: var(--brand-card);
  --color-brand-stroke: var(--brand-stroke);
  --color-brand-clicked: var(--brand-clicked);
  --color-brand-border-color: var(--brand-border-color);
  --color-brand-input: var(--brand-input);
  --color-brand-tick: var(--brand-tick);
  --color-brand-unpublish: var(--brand-unpublish);
  --color-brand-gradient: var(--brand-gradient);
  --color-brand-header-bg: var(--brand-header-bg);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-toast-text: var(--toast-text);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --font-satoshi: 'Satoshi', 'Geist', system-ui, sans-serif;
  /* Brand Custom Light Mode Colors    */
  --brand-primary: rgba(174, 0, 208, 1);
  --brand-secondary: rgba(123, 90, 255, 1);
  --brand-tertiary: rgba(18, 25, 94, 1);
  --brand-white-text: rgba(255, 255, 255, 1);
  --brand-primary-font: rgba(29, 31, 27, 1);
  --brand-secondary-font: rgba(107, 111, 105, 1);
  --brand-overlay: rgba(255, 255, 255, 0.7);
  --brand-card-hover: rgba(253, 244, 255, 1);
  --brand-background: rgba(250, 250, 250, 1);
  --brand-card: rgba(255, 255, 255, 1);
  --brand-stroke: rgba(176, 145, 182, 0.2);
  --brand-clicked: rgba(174, 0, 208, 0.1);
  --brand-border-color: rgba(80, 25, 94, 50);
  --brand-input: rgba(226, 232, 240, 100);
  --brand-tick: rgba(84, 188, 153, 1);
  --brand-unpublish: rgba(224, 30, 90, 1);
  --brand-gradient: linear-gradient(to right, #ae00d0, #7b5aff);
  --brand-header-bg: #121212;

  /* Workflow Card Colors - Light Mode */
  --container-Background: #FFFFFF;
  --Border_color: #E5E7EB;
  --icon-background: #F4F4F5;
  --metadata-text: #6F6F6F;
  --search-background: #FAFAFA;
  --search-text: #A1A1AA;
  --Input-black: #F9FAFB;
  --Border: #E5E7EB;
  --White: #000000;

  /* Custom Fonts */
  --font-primary: var(--font-sora);
  --font-secondary: var(--font-jost);

  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --toast-text: oklch(0.205 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  /* Brand Custom Dark Mode Colors    */
  --brand-primary: rgba(174, 0, 208, 1);
  --brand-secondary: rgba(123, 90, 255, 1);
  --brand-tertiary: rgba(18, 25, 94, 1);
  --brand-white-text: rgba(226, 215, 228, 1);
  --brand-primary-font: rgba(226, 215, 228, 1);
  --brand-secondary-font: rgba(231, 209, 235, 0.7);
  --brand-overlay: rgba(0, 0, 0, 0.9);
  --brand-card-hover: rgba(36, 24, 38, 1);
  --brand-background: rgba(11, 2, 13, 1);
  --brand-card: rgba(36, 24, 38, 1);
  --brand-stroke: rgba(176, 145, 182, 0.2);
  --brand-clicked: rgba(174, 0, 208, 0.1);
  --brand-border-color: rgba(80, 25, 94, 50);
  --brand-input: rgba(226, 232, 240, 100);
  --brand-tick: rgba(84, 188, 153, 1);
  --brand-unpublish: rgba(224, 30, 90, 1);
  --brand-gradient: linear-gradient(to right, #ae00d0, #7b5aff);
  --brand-header-bg: #121212;

  /* Workflow Card Colors - Dark Mode */
  --container-Background: #1E1E1E;
  --Border_color: #4B4B4D;
  --icon-background: #2E2E2E;
  --metadata-text: #6F6F6F;
  --search-background: #3F3F46;
  --search-text: #A1A1AA;
  --Input-black: #18181B;
  --Border: #3F3F46;
  --White: #FFFFFF;

  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
  --toast-text: oklch(0.205 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* In Tailwind v4 , by default buttons have default pointer, this is to override that to add pointer hover and not on disabled */
  button:not([disabled]),
  [role="button"]:not([disabled]) {
    cursor: pointer;
  }

  /* Custom scrollbar styles - improved for better user experience */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    opacity: 0;
  }

  .custom-scrollbar {
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: transparent transparent; /* For Firefox */
  }

  .custom-scrollbar:hover {
    scrollbar-color: #F4F4F5 transparent; /* For Firefox */
  }

  .dark .custom-scrollbar:hover {
    scrollbar-color: #4B4B4D transparent; /* For Firefox */
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px 0;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #F4F4F5;
    border-radius: 9999px;
    transition: all 0.2s ease;
    opacity: 0;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #4B4B4D;
  }

  .custom-scrollbar:hover::-webkit-scrollbar-thumb {
    opacity: 1;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #E5E7EB;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
  }

  /* Improve component spacing and transitions */
  .workflow-node {
    transition:
      box-shadow 0.2s ease,
      border-color 0.2s ease;
  }

  /* Ensure ReactFlow nodes don't have transform transitions during drag */
  .react-flow__node.dragging .workflow-node,
  .react-flow__node[data-dragging="true"] .workflow-node {
    transition: none !important;
  }

  /* Enhance accordion animations */
  .accordion-content-animation {
    transition: all 0.3s ease;
  }

  /* Sidebar background pattern - theme responsive */
  aside {
    background-image:
      radial-gradient(circle at 25px 25px, rgba(63, 114, 175, 0.05) 2%, transparent 0%),
      radial-gradient(circle at 75px 75px, rgba(63, 114, 175, 0.05) 2%, transparent 0%);
    background-size: 100px 100px;
  }

  /* Dark mode pattern */
  .dark aside {
    background-image:
      radial-gradient(circle at 25px 25px, rgba(29, 205, 159, 0.03) 2%, transparent 0%),
      radial-gradient(circle at 75px 75px, rgba(29, 205, 159, 0.03) 2%, transparent 0%);
    background-size: 100px 100px;
  }

  /* Hide scrollbars during drag operations */
  .dragging,
  .dragging * {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
  }

  .dragging::-webkit-scrollbar,
  .dragging *::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari, Opera */
  }

  /* Enhanced drag ghost appearance with complete cleanup */
  .drag-ghost {
    /* Preserve rounded corners */
    border-radius: 0.5rem !important;
    
    /* Clean background - use solid colors instead of variables for reliability */
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    
    /* Enhanced shadow for drag state */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25) !important;
    
    /* Clean up all visual artifacts */
    overflow: hidden !important;
    opacity: 0.95 !important;
    
    /* Remove any transforms that might cause issues */
    transform: none !important;
    
    /* Ensure clean positioning */
    position: relative !important;
    z-index: 9999 !important;
  }

  /* Dark mode drag ghost */
  .dark .drag-ghost {
    background: #1f1f23 !important;
    border: 1px solid #3f3f46 !important;
  }

  /* Completely hide scrollbars and clean up all child elements */
  .drag-ghost,
  .drag-ghost * {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
    
    /* Remove any background patterns or artifacts */
    background-image: none !important;
    background-attachment: initial !important;
    background-size: initial !important;
  }

  .drag-ghost::-webkit-scrollbar,
  .drag-ghost *::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  /* Clean up any container backgrounds during drag */
  .dragging .custom-scrollbar,
  .dragging .custom-scrollbar * {
    background: transparent !important;
    scrollbar-width: none !important;
  }

  .dragging .custom-scrollbar::-webkit-scrollbar,
  .dragging .custom-scrollbar *::-webkit-scrollbar {
    display: none !important;
  }
}

.sidebar-desc {
  color: #6B7280;
}
.dark .sidebar-desc {
  color: #6F6F6F;
}

@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/satoshi/Satoshi-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/satoshi/Satoshi-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Tailwind font utility for Satoshi */
@layer utilities {
  .font-\[Satoshi\] {
    font-family: 'Satoshi', 'Geist', system-ui, sans-serif !important;
  }
}

@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/satoshi/Satoshi-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/satoshi/Satoshi-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Tailwind font utility for Satoshi */
@layer utilities {
  .font-\[Satoshi\] {
    font-family: 'Satoshi', 'Geist', system-ui, sans-serif !important;
  }
}

/* Setting custom fonts in tailwind configuration */
@layer utilities {
  .brand-gradient-indicator {
    @apply bg-[image:var(--brand-gradient)];
  }

  .text-gradient-brand {
    @apply bg-[image:var(--brand-gradient)] bg-clip-text text-transparent;
  }

  .font-primary {
    font-family: var(--font-primary);
  }

  .font-secondary {
    font-family: var(--font-secondary);
  }

  .font-satoshi {
    font-family: var(--font-satoshi);
  }

  /* Animation delay utilities */
  .animation-delay-200 {
    animation-delay: 0.2s;
  }

  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  /* Futuristic animations for home page */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
  }

  .animate-fade-in {
    animation: fadeIn 1s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
  }

  /* Futuristic grid background */
  .grid-pattern {
    background-image:
      linear-gradient(rgba(174, 0, 208, 0.06) 1px, transparent 1px),
      linear-gradient(90deg, rgba(174, 0, 208, 0.06) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  .dark .grid-pattern {
    background-image:
      linear-gradient(rgba(174, 0, 208, 0.15) 1px, transparent 1px),
      linear-gradient(90deg, rgba(174, 0, 208, 0.15) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* MCP Logo consistent sizing */
  .mcp-logo-sidebar {
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
    min-height: 32px !important;
    max-width: 32px !important;
    max-height: 32px !important;
    object-fit: cover !important;
  }

  .mcp-logo-node {
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
    min-height: 20px !important;
    max-width: 20px !important;
    max-height: 20px !important;
    object-fit: cover !important;
  }
}

/* Futuristic keyframe animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(174, 0, 208, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(174, 0, 208, 0.6);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
